import { describe, it, expect, beforeEach } from '@jest/globals';
import { createUser, validateUpload, completeAccount } from '../handlers/user';
import { APIGatewayProxyEvent } from 'aws-lambda';

// Mock AWS clients
jest.mock('../lib/aws-clients', () => ({
  docClient: {
    send: jest.fn()
  },
  ENV: {
    USER_TABLE: 'test-users-table',
    TRANSFER_TABLE: 'test-transfers-table',
    AWS_REGION: 'us-east-2',
    BASE_URL: 'https://test.fasttransfer.com'
  }
}));

// Mock SendGrid
jest.mock('@sendgrid/mail', () => ({
  setApiKey: jest.fn(),
  send: jest.fn().mockResolvedValue([{ statusCode: 202 }])
}));

describe('User System Integration Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  const createMockEvent = (
    httpMethod: string,
    body?: any,
    headers?: Record<string, string>
  ): APIGatewayProxyEvent => ({
    httpMethod,
    body: body ? JSON.stringify(body) : null,
    headers: headers || {},
    multiValueHeaders: {},
    pathParameters: null,
    queryStringParameters: null,
    requestContext: {
      identity: {
        sourceIp: '127.0.0.1'
      }
    } as any,
    resource: '',
    path: '',
    isBase64Encoded: false,
    stageVariables: null,
    multiValueQueryStringParameters: null
  });

  describe('Complete User Flow Integration', () => {
    it('should handle complete user creation and upload flow', async () => {
      const mockSend = require('../lib/aws-clients').docClient.send;
      const mockEmailSend = require('@sendgrid/mail').send;

      // Step 1: Create new user
      mockSend
        .mockResolvedValueOnce({ Item: null }) // User doesn't exist
        .mockResolvedValueOnce({}) // User created
        .mockResolvedValueOnce({ Item: null }); // Rate limit check

      const createEvent = createMockEvent('POST', {
        email: '<EMAIL>'
      });

      const createResponse = await createUser(createEvent);
      expect(createResponse.statusCode).toBe(201);
      
      const createBody = JSON.parse(createResponse.body);
      expect(createBody.email).toBe('<EMAIL>');
      expect(createBody.status).toBe('pending');
      expect(mockEmailSend).toHaveBeenCalled(); // Welcome email sent

      // Step 2: Validate upload for pending user
      const pendingUser = {
        email: '<EMAIL>',
        status: 'pending',
        dataUploaded: 0,
        createdAt: Date.now(),
        updatedAt: Date.now()
      };

      mockSend
        .mockResolvedValueOnce({ Item: null }) // Rate limit check
        .mockResolvedValueOnce({ Item: pendingUser }); // Get user

      const validateEvent = createMockEvent('POST', {
        email: '<EMAIL>',
        uploadSize: 50 * 1024 * 1024 // 50MB
      });

      const validateResponse = await validateUpload(validateEvent);
      expect(validateResponse.statusCode).toBe(200);
      
      const validateBody = JSON.parse(validateResponse.body);
      expect(validateBody.canUpload).toBe(true);
      expect(validateBody.upgradeRequired).toBe(false);

      // Step 3: Complete account registration
      mockSend
        .mockResolvedValueOnce({ Item: pendingUser }) // Get pending user
        .mockResolvedValueOnce({}) // Update user to active
        .mockResolvedValueOnce({ 
          Item: { 
            ...pendingUser, 
            status: 'active',
            name: 'Test User',
            activatedAt: Date.now()
          }
        }); // Get updated user

      const completeEvent = createMockEvent('POST', {
        email: '<EMAIL>',
        token: 'verification-token-123',
        name: 'Test User'
      });

      const completeResponse = await completeAccount(completeEvent);
      expect(completeResponse.statusCode).toBe(200);
      
      const completeBody = JSON.parse(completeResponse.body);
      expect(completeBody.status).toBe('active');
      expect(completeBody.message).toContain('1TB');

      // Step 4: Validate large upload for active user
      const activeUser = {
        email: '<EMAIL>',
        status: 'active',
        dataUploaded: 0,
        name: 'Test User',
        createdAt: Date.now(),
        updatedAt: Date.now(),
        activatedAt: Date.now()
      };

      mockSend
        .mockResolvedValueOnce({ Item: null }) // Rate limit check
        .mockResolvedValueOnce({ Item: activeUser }); // Get active user

      const largeValidateEvent = createMockEvent('POST', {
        email: '<EMAIL>',
        uploadSize: 500 * 1024 * 1024 * 1024 // 500GB
      });

      const largeValidateResponse = await validateUpload(largeValidateEvent);
      expect(largeValidateResponse.statusCode).toBe(200);
      
      const largeValidateBody = JSON.parse(largeValidateResponse.body);
      expect(largeValidateBody.canUpload).toBe(true);
      expect(largeValidateBody.upgradeRequired).toBe(false);
    });

    it('should handle upload limit exceeded scenario', async () => {
      const mockSend = require('../lib/aws-clients').docClient.send;
      const mockEmailSend = require('@sendgrid/mail').send;

      // User with 95MB already uploaded
      const nearLimitUser = {
        email: '<EMAIL>',
        status: 'pending',
        dataUploaded: 95 * 1024 * 1024, // 95MB
        createdAt: Date.now(),
        updatedAt: Date.now()
      };

      mockSend
        .mockResolvedValueOnce({ Item: null }) // Rate limit check
        .mockResolvedValueOnce({ Item: nearLimitUser }); // Get user

      const validateEvent = createMockEvent('POST', {
        email: '<EMAIL>',
        uploadSize: 10 * 1024 * 1024 // 10MB (would exceed 100MB limit)
      });

      const validateResponse = await validateUpload(validateEvent);
      expect(validateResponse.statusCode).toBe(200);
      
      const validateBody = JSON.parse(validateResponse.body);
      expect(validateBody.canUpload).toBe(false);
      expect(validateBody.upgradeRequired).toBe(true);
      expect(validateBody.message).toContain('upgrade');
    });

    it('should handle rate limiting correctly', async () => {
      const mockSend = require('../lib/aws-clients').docClient.send;

      // Mock rate limit exceeded
      mockSend.mockResolvedValueOnce({ 
        Item: { 
          transferId: 'user_creation:127.0.0.1:1234567890000',
          requestCount: 5, // At limit
          resetTime: Date.now() + 900000 // 15 minutes from now
        }
      });

      const createEvent = createMockEvent('POST', {
        email: '<EMAIL>'
      });

      const createResponse = await createUser(createEvent);
      expect(createResponse.statusCode).toBe(429);
      
      const createBody = JSON.parse(createResponse.body);
      expect(createBody.error).toContain('Too many');
    });

    it('should handle invalid input gracefully', async () => {
      // Test invalid email
      const invalidEmailEvent = createMockEvent('POST', {
        email: 'invalid-email'
      });

      const invalidEmailResponse = await createUser(invalidEmailEvent);
      expect(invalidEmailResponse.statusCode).toBe(400);
      
      const invalidEmailBody = JSON.parse(invalidEmailResponse.body);
      expect(invalidEmailBody.error).toContain('Valid email');

      // Test missing email
      const missingEmailEvent = createMockEvent('POST', {});

      const missingEmailResponse = await createUser(missingEmailEvent);
      expect(missingEmailResponse.statusCode).toBe(400);
      
      const missingEmailBody = JSON.parse(missingEmailResponse.body);
      expect(missingEmailBody.error).toContain('required');

      // Test invalid upload size
      const mockSend = require('../lib/aws-clients').docClient.send;
      mockSend.mockResolvedValueOnce({ Item: null }); // Rate limit check

      const invalidSizeEvent = createMockEvent('POST', {
        email: '<EMAIL>',
        uploadSize: -1
      });

      const invalidSizeResponse = await validateUpload(invalidSizeEvent);
      expect(invalidSizeResponse.statusCode).toBe(400);
      
      const invalidSizeBody = JSON.parse(invalidSizeResponse.body);
      expect(invalidSizeBody.error).toContain('Valid upload size');
    });

    it('should handle CORS preflight requests', async () => {
      const optionsEvent = createMockEvent('OPTIONS');

      const createResponse = await createUser(optionsEvent);
      expect(createResponse.statusCode).toBe(200);

      const validateResponse = await validateUpload(optionsEvent);
      expect(validateResponse.statusCode).toBe(200);

      const completeResponse = await completeAccount(optionsEvent);
      expect(completeResponse.statusCode).toBe(200);
    });
  });

  describe('Error Handling', () => {
    it('should handle DynamoDB errors gracefully', async () => {
      const mockSend = require('../lib/aws-clients').docClient.send;
      
      // Mock DynamoDB error
      mockSend.mockRejectedValueOnce(new Error('DynamoDB connection failed'));

      const createEvent = createMockEvent('POST', {
        email: '<EMAIL>'
      });

      const createResponse = await createUser(createEvent);
      expect(createResponse.statusCode).toBe(500);
      
      const createBody = JSON.parse(createResponse.body);
      expect(createBody.error).toBe('Internal server error');
    });

    it('should handle email service errors gracefully', async () => {
      const mockSend = require('../lib/aws-clients').docClient.send;
      const mockEmailSend = require('@sendgrid/mail').send;

      // Mock successful user creation but email failure
      mockSend
        .mockResolvedValueOnce({ Item: null }) // User doesn't exist
        .mockResolvedValueOnce({}) // User created
        .mockResolvedValueOnce({ Item: null }); // Rate limit check

      // Mock email failure
      mockEmailSend.mockRejectedValueOnce(new Error('SendGrid API error'));

      const createEvent = createMockEvent('POST', {
        email: '<EMAIL>'
      });

      // Should still succeed even if email fails
      const createResponse = await createUser(createEvent);
      expect(createResponse.statusCode).toBe(201);
    });
  });
});

// Update AWS clients to use us-east-2 region
import { DynamoDBClient } from '@aws-sdk/client-dynamodb';
import { DynamoDBDocumentClient } from '@aws-sdk/lib-dynamodb';
import { S3Client } from '@aws-sdk/client-s3';
import { SQSClient } from '@aws-sdk/client-sqs';

// Environment variables
export const ENV = {
  REGION:  'us-east-2',
  TRANSFER_TABLE: process.env.TRANSFER_TABLE || 'FastTransfer-Transfers',
  USER_TABLE: process.env.USER_TABLE || 'FastTransfer-Users',
  JOB_TABLE: process.env.JOB_TABLE || 'FastTransfer-Jobs',
  UPLOAD_BUCKET: process.env.UPLOAD_BUCKET || 'transfer-yeehawboost-upload',
  COMPRESSED_BUCKET: process.env.COMPRESSED_BUCKET || 'transfer-yeehawboost-compressed',
  DECOMPRESSED_BUCKET: process.env.DECOMPRESSED_BUCKET || 'transfer-yeehawboost-decompressed',
  COMPRESSION_QUEUE_URL: process.env.COMPRESSION_QUEUE_URL || 'https://sqs.us-east-2.amazonaws.com/117255351486/FastTransfer-CompressionJobs',
  DECOMPRESSION_QUEUE_URL: process.env.DECOMPRESSION_QUEUE_URL || 'https://sqs.us-east-2.amazonaws.com/117255351486/FastTransfer-DecompressionJobs',
  CLOUDFRONT_DOMAIN: process.env.CLOUDFRONT_DOMAIN || 'd1j3tjpa9wjs97.cloudfront.net',
  BASE_URL: process.env.BASE_URL || 'https://transfer.yeehawboost.com',
  FRONTEND_URL: process.env.FRONTEND_URL || 'https://transfer.yeehawboost.com',
  MAX_FILE_SIZE: process.env.MAX_FILE_SIZE || '107374182400',
  FROM_EMAIL: process.env.FROM_EMAIL || '<EMAIL>',
};

// Add SendGrid configuration
export const SENDGRID_CONFIG = {
  API_KEY: process.env.SENDGRID_API_KEY || '*********************************************************************',
  FROM_EMAIL: process.env.FROM_EMAIL || '<EMAIL>',
  TEMPLATES: {
    SHARE: 'd-f3b4c5a6b7c8d9e0f1a2b3c4',
    WELCOME: 'd-a1b2c3d4e5f6g7h8i9j0k1l2',
  }
};

// Create clients with the correct region
const dynamoClient = new DynamoDBClient({ 
  region: ENV.REGION
});

// Export the clients
export const s3Client = new S3Client({ 
  region: ENV.REGION
});

export const sqsClient = new SQSClient({ 
  region: ENV.REGION
});

// Export document client
export const docClient = DynamoDBDocumentClient.from(dynamoClient);

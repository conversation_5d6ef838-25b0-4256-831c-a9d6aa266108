#!/usr/bin/env node

/**
 * FastTransfer Performance Testing Script
 * 
 * This script tests the performance improvements after CDN and optimization setup.
 * It measures:
 * - Bundle sizes
 * - Load times
 * - Cache headers
 * - Image optimization results
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Colors for output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function formatBytes(bytes) {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function analyzeBundleSize() {
  log('\n📦 Analyzing Bundle Sizes', 'cyan');
  log('=' .repeat(50), 'cyan');
  
  const distPath = path.join(__dirname, '../packages/frontend/dist');
  
  if (!fs.existsSync(distPath)) {
    log('❌ Build directory not found. Run npm run build first.', 'red');
    return;
  }
  
  const assetsPath = path.join(distPath, 'assets');
  if (!fs.existsSync(assetsPath)) {
    log('❌ Assets directory not found.', 'red');
    return;
  }
  
  // Get all files recursively from assets directory
  function getAllFiles(dir, fileList = []) {
    const files = fs.readdirSync(dir);
    files.forEach(file => {
      const filePath = path.join(dir, file);
      if (fs.statSync(filePath).isDirectory()) {
        getAllFiles(filePath, fileList);
      } else {
        fileList.push({ name: file, path: filePath, relativePath: path.relative(assetsPath, filePath) });
      }
    });
    return fileList;
  }

  const allFiles = getAllFiles(assetsPath);
  const jsFiles = allFiles.filter(f => f.name.endsWith('.js'));
  const cssFiles = allFiles.filter(f => f.name.endsWith('.css'));
  const imageFiles = allFiles.filter(f => /\.(png|jpg|jpeg|gif|svg|webp)$/i.test(f.name));
  
  let totalSize = 0;
  let jsSize = 0;
  let cssSize = 0;
  let imageSize = 0;
  
  // Analyze JavaScript files
  log('\n🟨 JavaScript Files:', 'yellow');
  jsFiles.forEach(file => {
    const stats = fs.statSync(file.path);
    const size = stats.size;
    jsSize += size;
    totalSize += size;

    const type = file.name.includes('vendor') ? 'vendor' :
                 file.name.includes('index') ? 'main' : 'chunk';
    log(`  ${file.relativePath} (${type}): ${formatBytes(size)}`, 'yellow');
  });
  log(`  Total JS: ${formatBytes(jsSize)}`, 'yellow');

  // Analyze CSS files
  log('\n🟦 CSS Files:', 'blue');
  cssFiles.forEach(file => {
    const stats = fs.statSync(file.path);
    const size = stats.size;
    cssSize += size;
    totalSize += size;
    log(`  ${file.relativePath}: ${formatBytes(size)}`, 'blue');
  });
  log(`  Total CSS: ${formatBytes(cssSize)}`, 'blue');

  // Analyze image files
  if (imageFiles.length > 0) {
    log('\n🖼️  Image Files:', 'magenta');
    imageFiles.forEach(file => {
      const stats = fs.statSync(file.path);
      const size = stats.size;
      imageSize += size;
      totalSize += size;
      log(`  ${file.relativePath}: ${formatBytes(size)}`, 'magenta');
    });
    log(`  Total Images: ${formatBytes(imageSize)}`, 'magenta');
  }
  
  log('\n📊 Bundle Summary:', 'green');
  log(`  JavaScript: ${formatBytes(jsSize)} (${((jsSize/totalSize)*100).toFixed(1)}%)`, 'green');
  log(`  CSS: ${formatBytes(cssSize)} (${((cssSize/totalSize)*100).toFixed(1)}%)`, 'green');
  log(`  Images: ${formatBytes(imageSize)} (${((imageSize/totalSize)*100).toFixed(1)}%)`, 'green');
  log(`  Total Assets: ${formatBytes(totalSize)}`, 'green');
  
  // Check for code splitting
  const vendorChunks = jsFiles.filter(f => f.name.includes('vendor')).length;
  log(`\n🔄 Code Splitting: ${vendorChunks} vendor chunks detected`, vendorChunks > 0 ? 'green' : 'yellow');
  
  return {
    totalSize,
    jsSize,
    cssSize,
    imageSize,
    fileCount: allFiles.length,
    vendorChunks
  };
}

function checkOptimizations() {
  log('\n⚡ Checking Build Optimizations', 'cyan');
  log('=' .repeat(50), 'cyan');
  
  const configPath = path.join(__dirname, '../packages/frontend/vite.config.ts');
  
  if (!fs.existsSync(configPath)) {
    log('❌ Vite config not found.', 'red');
    return;
  }
  
  const config = fs.readFileSync(configPath, 'utf8');
  
  const checks = [
    { name: 'Code Splitting', pattern: /manualChunks/, enabled: config.includes('manualChunks') },
    { name: 'Terser Minification', pattern: /terser/, enabled: config.includes('terser') },
    { name: 'Image Optimization', pattern: /viteImagemin/, enabled: config.includes('viteImagemin') },
    { name: 'Build Optimizations', pattern: /rollupOptions/, enabled: config.includes('rollupOptions') },
  ];
  
  checks.forEach(check => {
    const status = check.enabled ? '✅' : '❌';
    const color = check.enabled ? 'green' : 'red';
    log(`  ${status} ${check.name}`, color);
  });
  
  return checks.every(check => check.enabled);
}

function testCacheHeaders() {
  log('\n🗄️  Testing Cache Headers', 'cyan');
  log('=' .repeat(50), 'cyan');
  
  // This would require the server to be running
  // For now, just check if the cache configuration exists in server.ts
  const serverPath = path.join(__dirname, '../services/backend/src/server.ts');
  
  if (!fs.existsSync(serverPath)) {
    log('❌ Server file not found.', 'red');
    return false;
  }
  
  const serverCode = fs.readFileSync(serverPath, 'utf8');
  
  const cacheChecks = [
    { name: 'Static Asset Caching', pattern: /Cache-Control.*max-age/, enabled: serverCode.includes('Cache-Control') },
    { name: 'Immutable Assets', pattern: /immutable/, enabled: serverCode.includes('immutable') },
    { name: 'HTML Cache Headers', pattern: /must-revalidate/, enabled: serverCode.includes('must-revalidate') },
    { name: 'Asset Path Handling', pattern: /\/assets/, enabled: serverCode.includes('/assets') },
  ];
  
  cacheChecks.forEach(check => {
    const status = check.enabled ? '✅' : '❌';
    const color = check.enabled ? 'green' : 'red';
    log(`  ${status} ${check.name}`, color);
  });
  
  return cacheChecks.every(check => check.enabled);
}

function checkCloudFrontConfig() {
  log('\n☁️  Checking CloudFront Configuration', 'cyan');
  log('=' .repeat(50), 'cyan');
  
  const stackPath = path.join(__dirname, '../infrastructure/lib/fast-transfer-stack.ts');
  
  if (!fs.existsSync(stackPath)) {
    log('❌ CDK stack file not found.', 'red');
    return false;
  }
  
  const stackCode = fs.readFileSync(stackPath, 'utf8');
  
  const cdnChecks = [
    { name: 'CloudFront Distribution', pattern: /cloudfront\.Distribution/, enabled: stackCode.includes('cloudfront.Distribution') },
    { name: 'Cache Behaviors', pattern: /additionalBehaviors/, enabled: stackCode.includes('additionalBehaviors') },
    { name: 'Origin Access Control', pattern: /OriginAccessControl/, enabled: stackCode.includes('OriginAccessControl') },
    { name: 'Security Headers', pattern: /responseHeadersPolicy/, enabled: stackCode.includes('responseHeadersPolicy') },
    { name: 'HTTP/2 Support', pattern: /HTTP2_AND_3/, enabled: stackCode.includes('HTTP2_AND_3') },
  ];
  
  cdnChecks.forEach(check => {
    const status = check.enabled ? '✅' : '❌';
    const color = check.enabled ? 'green' : 'red';
    log(`  ${status} ${check.name}`, color);
  });
  
  return cdnChecks.every(check => check.enabled);
}

function generateReport(bundleAnalysis) {
  log('\n📋 Performance Report', 'cyan');
  log('=' .repeat(50), 'cyan');
  
  const optimizationsEnabled = checkOptimizations();
  const cacheHeadersConfigured = testCacheHeaders();
  const cloudFrontConfigured = checkCloudFrontConfig();
  
  const score = [optimizationsEnabled, cacheHeadersConfigured, cloudFrontConfigured]
    .filter(Boolean).length;
  
  log('\n🎯 Overall Score:', 'green');
  log(`  ${score}/3 optimization categories completed`, score === 3 ? 'green' : 'yellow');
  
  if (bundleAnalysis) {
    log('\n📈 Performance Metrics:', 'blue');
    log(`  Total bundle size: ${formatBytes(bundleAnalysis.totalSize)}`, 'blue');
    log(`  Code splitting: ${bundleAnalysis.vendorChunks > 0 ? 'Enabled' : 'Disabled'}`, 
        bundleAnalysis.vendorChunks > 0 ? 'green' : 'yellow');
  }
  
  log('\n🚀 Next Steps:', 'yellow');
  if (!optimizationsEnabled) {
    log('  - Complete Vite build optimizations', 'yellow');
  }
  if (!cacheHeadersConfigured) {
    log('  - Configure cache headers in backend', 'yellow');
  }
  if (!cloudFrontConfigured) {
    log('  - Set up CloudFront CDN configuration', 'yellow');
  }
  if (score === 3) {
    log('  - Deploy to production and test real-world performance', 'green');
    log('  - Monitor CDN cache hit rates', 'green');
    log('  - Run Lighthouse performance audits', 'green');
  }
}

function main() {
  log('🚀 FastTransfer Performance Analysis', 'cyan');
  log('=' .repeat(50), 'cyan');
  
  const bundleAnalysis = analyzeBundleSize();
  generateReport(bundleAnalysis);
  
  log('\n✨ Analysis complete!', 'green');
}

// Run the analysis
if (require.main === module) {
  main();
}

module.exports = {
  analyzeBundleSize,
  checkOptimizations,
  testCacheHeaders,
  checkCloudFrontConfig,
  generateReport
};

#!/bin/bash

# Function to display help
show_help() {
    echo "Video Compression Script"
    echo "========================"
    echo ""
    echo "USAGE:"
    echo "  $0 <file_extension>"
    echo "  $0 -help | --help | -h"
    echo ""
    echo "DESCRIPTION:"
    echo "  Compresses video files of specified format to MP4 with smaller file size."
    echo "  Original files are moved to 'original/' folder after successful compression."
    echo "  Compressed files are saved to 'encoded/' folder with '_zmt' suffix."
    echo ""
    echo "EXAMPLES:"
    echo "  $0 mp4     # Compress all .mp4 files"
    echo "  $0 flv     # Compress all .flv files"
    echo "  $0 mkv     # Compress all .mkv files"
    echo "  $0 3gp     # Compress all .3gp files"
    echo "  $0 avi     # Compress all .avi files"
    echo "  $0 mov     # Compress all .mov files"
    echo "  $0 webm    # Compress all .webm files"
    echo ""
    echo "OUTPUT:"
    echo "  - Shows processing time for each file"
    echo "  - Displays file size reduction percentage"
    echo "  - Provides summary with total time and success rate"
    echo ""
}

# Check for help flags
if [[ "$1" == "-help" || "$1" == "--help" || "$1" == "-h" ]]; then
    show_help
    exit 0
fi

# Check if file extension argument is provided
if [ $# -eq 0 ]; then
    echo "Error: No file extension specified"
    echo ""
    show_help
    exit 1
fi

# Get file extension from argument
extension="$1"

# Create directories if they don't exist
mkdir -p encoded original

echo "Starting $extension compression..."
start_time=$(date +%s)
file_count=0
successful_count=0
total_original_size=0
total_compressed_size=0

for f in *."$extension"; do
    # Skip if no files found
    if [[ "$f" == "*.$extension" ]]; then
        echo "No .$extension files found in current directory"
        exit 1
    fi
    
    file_count=$((file_count + 1))
    echo "Processing file $file_count: $f"
    
    file_start=$(date +%s)
    
    # Compression settings for smaller file size
    if ffmpeg -y -hide_banner -loglevel error \
        -i "$f" \
        -vcodec libx264 \
        -crf 28 \
        -preset fast \
        -vf "scale=trunc(iw*0.8/2)*2:trunc(ih*0.8/2)*2" \
        -r 23.976 \
        -movflags +faststart \
        -pix_fmt yuv420p \
        "encoded/${f%.*}_zmt.mp4"; then
        
        # Move original file after successful encoding
        mv "$f" "original/"
        
        file_end=$(date +%s)
        file_duration=$((file_end - file_start))
        successful_count=$((successful_count + 1))
        
        # Get file sizes for comparison
        original_size=$(stat -f%z "original/$f" 2>/dev/null || stat -c%s "original/$f" 2>/dev/null)
        compressed_size=$(stat -f%z "encoded/${f%.*}_zmt.mp4" 2>/dev/null || stat -c%s "encoded/${f%.*}_zmt.mp4" 2>/dev/null)
        
        if [[ -n "$original_size" && -n "$compressed_size" ]]; then
            # Calculate compression percentage (how much of original size remains)
            compression_percent=$((compressed_size * 100 / original_size))
            reduction_percent=$((100 - compression_percent))
            
            original_mb=$((original_size / 1024 / 1024))
            compressed_mb=$((compressed_size / 1024 / 1024))
            
            # Add to totals
            total_original_size=$((total_original_size + original_size))
            total_compressed_size=$((total_compressed_size + compressed_size))
            
            echo "✓ Completed: $f (${file_duration}s) - ${original_mb}MB → ${compressed_mb}MB (${compression_percent}% of original, ${reduction_percent}% reduction)"
        else
            echo "✓ Completed: $f (${file_duration}s)"
        fi
    else
        echo "✗ Failed to encode: $f"
    fi
done

end_time=$(date +%s)
total_duration=$((end_time - start_time))
minutes=$((total_duration / 60))
seconds=$((total_duration % 60))

echo ""
echo "========== SUMMARY =========="
echo "Extension: .$extension"
echo "Files processed: $file_count"
echo "Successfully encoded: $successful_count"
echo "Failed: $((file_count - successful_count))"

# Calculate overall compression statistics
if [[ $total_original_size -gt 0 && $total_compressed_size -gt 0 ]]; then
    overall_compression_percent=$((total_compressed_size * 100 / total_original_size))
    overall_reduction_percent=$((100 - overall_compression_percent))
    
    total_original_mb=$((total_original_size / 1024 / 1024))
    total_compressed_mb=$((total_compressed_size / 1024 / 1024))
    
    echo "Original total size: ${total_original_mb}MB"
    echo "Compressed total size: ${total_compressed_mb}MB"
    echo "Overall compression: ${overall_compression_percent}% of original size"
    echo "Overall reduction: ${overall_reduction_percent}%"
fi

echo "Total time: ${minutes}m ${seconds}s"
echo "Finished!"

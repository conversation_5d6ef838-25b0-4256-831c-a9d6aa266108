#!/bin/bash

# FastTransfer Frontend Deployment Script
# This script builds the frontend and deploys it to S3 with CloudFront invalidation

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
FRONTEND_DIR="packages/frontend"
BUILD_DIR="$FRONTEND_DIR/dist"
S3_BUCKET_NAME="${S3_BUCKET_NAME:-fasttransfer-static-\$AWS_ACCOUNT_ID-\$AWS_REGION}"
CLOUDFRONT_DISTRIBUTION_ID="${CLOUDFRONT_DISTRIBUTION_ID}"
AWS_PROFILE="${AWS_PROFILE:-default}"

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

check_dependencies() {
    log_info "Checking dependencies..."
    
    # Check if AWS CLI is installed
    if ! command -v aws &> /dev/null; then
        log_error "AWS CLI is not installed. Please install it first."
        exit 1
    fi
    
    # Check if Node.js is installed
    if ! command -v node &> /dev/null; then
        log_error "Node.js is not installed. Please install it first."
        exit 1
    fi
    
    # Check if npm is installed
    if ! command -v npm &> /dev/null; then
        log_error "npm is not installed. Please install it first."
        exit 1
    fi
    
    log_success "All dependencies are available"
}

build_frontend() {
    log_info "Building frontend application..."
    
    cd "$FRONTEND_DIR"
    
    # Install dependencies if node_modules doesn't exist
    if [ ! -d "node_modules" ]; then
        log_info "Installing dependencies..."
        npm install
    fi
    
    # Build the application
    log_info "Running production build..."
    npm run build
    
    # Check if build was successful
    if [ ! -d "dist" ]; then
        log_error "Build failed - dist directory not found"
        exit 1
    fi
    
    cd - > /dev/null
    log_success "Frontend build completed"
}

deploy_to_s3() {
    log_info "Deploying to S3 bucket: $S3_BUCKET_NAME"
    
    # Check if bucket exists
    if ! aws s3 ls "s3://$S3_BUCKET_NAME" --profile "$AWS_PROFILE" &> /dev/null; then
        log_error "S3 bucket '$S3_BUCKET_NAME' does not exist or is not accessible"
        exit 1
    fi
    
    # Sync files to S3 with appropriate cache headers
    log_info "Uploading static assets..."
    
    # Upload versioned assets (JS, CSS) with long cache
    aws s3 sync "$BUILD_DIR/assets" "s3://$S3_BUCKET_NAME/assets" \
        --profile "$AWS_PROFILE" \
        --cache-control "public, max-age=31536000, immutable" \
        --delete
    
    # Upload HTML files with short cache
    aws s3 sync "$BUILD_DIR" "s3://$S3_BUCKET_NAME" \
        --profile "$AWS_PROFILE" \
        --exclude "assets/*" \
        --cache-control "public, max-age=300, must-revalidate" \
        --delete
    
    log_success "Files uploaded to S3"
}

invalidate_cloudfront() {
    if [ -z "$CLOUDFRONT_DISTRIBUTION_ID" ]; then
        log_warning "CloudFront distribution ID not provided, skipping invalidation"
        return
    fi
    
    log_info "Invalidating CloudFront distribution: $CLOUDFRONT_DISTRIBUTION_ID"
    
    # Create invalidation
    INVALIDATION_ID=$(aws cloudfront create-invalidation \
        --distribution-id "$CLOUDFRONT_DISTRIBUTION_ID" \
        --paths "/*" \
        --profile "$AWS_PROFILE" \
        --query 'Invalidation.Id' \
        --output text)
    
    if [ $? -eq 0 ]; then
        log_success "CloudFront invalidation created: $INVALIDATION_ID"
        log_info "Invalidation may take 5-15 minutes to complete"
    else
        log_error "Failed to create CloudFront invalidation"
        exit 1
    fi
}

show_usage() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  -b, --bucket BUCKET_NAME    S3 bucket name (default: from env)"
    echo "  -d, --distribution ID       CloudFront distribution ID"
    echo "  -p, --profile PROFILE       AWS profile to use (default: default)"
    echo "  -h, --help                  Show this help message"
    echo ""
    echo "Environment variables:"
    echo "  S3_BUCKET_NAME             S3 bucket name"
    echo "  CLOUDFRONT_DISTRIBUTION_ID CloudFront distribution ID"
    echo "  AWS_PROFILE                AWS profile to use"
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -b|--bucket)
            S3_BUCKET_NAME="$2"
            shift 2
            ;;
        -d|--distribution)
            CLOUDFRONT_DISTRIBUTION_ID="$2"
            shift 2
            ;;
        -p|--profile)
            AWS_PROFILE="$2"
            shift 2
            ;;
        -h|--help)
            show_usage
            exit 0
            ;;
        *)
            log_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Main execution
main() {
    log_info "Starting FastTransfer frontend deployment..."
    
    # Validate required parameters
    if [ -z "$S3_BUCKET_NAME" ]; then
        log_error "S3 bucket name is required. Set S3_BUCKET_NAME environment variable or use --bucket option."
        exit 1
    fi
    
    check_dependencies
    build_frontend
    deploy_to_s3
    invalidate_cloudfront
    
    log_success "Deployment completed successfully!"
    log_info "Your application should be available at the CloudFront distribution URL"
}

# Run main function
main "$@"

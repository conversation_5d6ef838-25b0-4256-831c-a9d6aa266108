#!/usr/bin/env node

/**
 * FastTransfer Cache Behavior Testing Script
 * 
 * This script tests the custom cache behaviors for different file types
 * by making HTTP requests and analyzing the response headers.
 */

const https = require('https');
const http = require('http');
const fs = require('fs');
const path = require('path');

// Configuration
const config = {
  baseUrl: process.env.TEST_URL || 'http://localhost:3000',
  testFiles: [
    { path: '/test.zmt', expectedMaxAge: 604800, type: 'ZMT Compressed' },
    { path: '/test.zip', expectedMaxAge: 604800, type: 'Archive' },
    { path: '/test.pdf', expectedMaxAge: 86400, type: 'Document' },
    { path: '/test.mp4', expectedMaxAge: 2592000, type: 'Media' },
    { path: '/test.html', expectedMaxAge: 300, type: 'HTML' },
    { path: '/test.txt', expectedMaxAge: 3600, type: 'Text' },
    { path: '/test.png', expectedMaxAge: 86400, type: 'Image' },
    { path: '/test.woff2', expectedMaxAge: 2592000, type: 'Font' },
    { path: '/assets/test-abc123.js', expectedMaxAge: 31536000, type: 'Versioned Asset' },
    { path: '/test.js', expectedMaxAge: 3600, type: 'JavaScript' },
    { path: '/test.css', expectedMaxAge: 3600, type: 'CSS' }
  ]
};

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

/**
 * Make HTTP request and return headers
 */
function makeRequest(url) {
  return new Promise((resolve, reject) => {
    const isHttps = url.startsWith('https:');
    const client = isHttps ? https : http;
    
    const req = client.request(url, { method: 'HEAD' }, (res) => {
      resolve({
        statusCode: res.statusCode,
        headers: res.headers
      });
    });
    
    req.on('error', reject);
    req.setTimeout(5000, () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });
    
    req.end();
  });
}

/**
 * Parse Cache-Control header to extract max-age
 */
function parseCacheControl(cacheControl) {
  if (!cacheControl) return null;
  
  const maxAgeMatch = cacheControl.match(/max-age=(\d+)/);
  return maxAgeMatch ? parseInt(maxAgeMatch[1]) : null;
}

/**
 * Format duration in human-readable format
 */
function formatDuration(seconds) {
  if (seconds >= 31536000) return `${Math.round(seconds / 31536000)} year(s)`;
  if (seconds >= 2592000) return `${Math.round(seconds / 2592000)} month(s)`;
  if (seconds >= 86400) return `${Math.round(seconds / 86400)} day(s)`;
  if (seconds >= 3600) return `${Math.round(seconds / 3600)} hour(s)`;
  if (seconds >= 60) return `${Math.round(seconds / 60)} minute(s)`;
  return `${seconds} second(s)`;
}

/**
 * Test cache behavior for a specific file type
 */
async function testCacheBehavior(testFile) {
  const url = `${config.baseUrl}${testFile.path}`;
  
  try {
    const response = await makeRequest(url);
    const cacheControl = response.headers['cache-control'];
    const actualMaxAge = parseCacheControl(cacheControl);
    
    const result = {
      path: testFile.path,
      type: testFile.type,
      statusCode: response.statusCode,
      cacheControl,
      actualMaxAge,
      expectedMaxAge: testFile.expectedMaxAge,
      passed: actualMaxAge === testFile.expectedMaxAge,
      headers: {
        contentType: response.headers['content-type'],
        contentEncoding: response.headers['content-encoding'],
        acceptRanges: response.headers['accept-ranges'],
        vary: response.headers['vary'],
        xContentTypeOptions: response.headers['x-content-type-options'],
        xFrameOptions: response.headers['x-frame-options']
      }
    };
    
    return result;
  } catch (error) {
    return {
      path: testFile.path,
      type: testFile.type,
      error: error.message,
      passed: false
    };
  }
}

/**
 * Print test result
 */
function printResult(result) {
  const status = result.passed ? 
    `${colors.green}✓ PASS${colors.reset}` : 
    `${colors.red}✗ FAIL${colors.reset}`;
  
  console.log(`\n${colors.bright}${result.type}${colors.reset} (${result.path})`);
  console.log(`Status: ${status}`);
  
  if (result.error) {
    console.log(`${colors.red}Error: ${result.error}${colors.reset}`);
    return;
  }
  
  console.log(`HTTP Status: ${result.statusCode}`);
  console.log(`Cache-Control: ${result.cacheControl || 'Not set'}`);
  
  if (result.actualMaxAge !== null) {
    const actualDuration = formatDuration(result.actualMaxAge);
    const expectedDuration = formatDuration(result.expectedMaxAge);
    
    console.log(`Max-Age: ${actualDuration} (expected: ${expectedDuration})`);
    
    if (!result.passed) {
      console.log(`${colors.yellow}Expected max-age: ${result.expectedMaxAge}s${colors.reset}`);
      console.log(`${colors.yellow}Actual max-age: ${result.actualMaxAge}s${colors.reset}`);
    }
  }
  
  // Print relevant headers
  const headers = result.headers;
  if (headers.contentType) console.log(`Content-Type: ${headers.contentType}`);
  if (headers.contentEncoding) console.log(`Content-Encoding: ${headers.contentEncoding}`);
  if (headers.acceptRanges) console.log(`Accept-Ranges: ${headers.acceptRanges}`);
  if (headers.vary) console.log(`Vary: ${headers.vary}`);
  if (headers.xContentTypeOptions) console.log(`X-Content-Type-Options: ${headers.xContentTypeOptions}`);
  if (headers.xFrameOptions) console.log(`X-Frame-Options: ${headers.xFrameOptions}`);
}

/**
 * Generate summary report
 */
function generateSummary(results) {
  const total = results.length;
  const passed = results.filter(r => r.passed).length;
  const failed = total - passed;
  
  console.log(`\n${colors.bright}=== CACHE BEHAVIOR TEST SUMMARY ===${colors.reset}`);
  console.log(`Total Tests: ${total}`);
  console.log(`${colors.green}Passed: ${passed}${colors.reset}`);
  console.log(`${colors.red}Failed: ${failed}${colors.reset}`);
  console.log(`Success Rate: ${Math.round((passed / total) * 100)}%`);
  
  if (failed > 0) {
    console.log(`\n${colors.red}Failed Tests:${colors.reset}`);
    results.filter(r => !r.passed).forEach(result => {
      console.log(`- ${result.type} (${result.path}): ${result.error || 'Cache policy mismatch'}`);
    });
  }
  
  // Performance insights
  console.log(`\n${colors.bright}=== CACHE PERFORMANCE INSIGHTS ===${colors.reset}`);
  
  const longTermCache = results.filter(r => r.actualMaxAge >= 86400).length;
  const mediumTermCache = results.filter(r => r.actualMaxAge >= 3600 && r.actualMaxAge < 86400).length;
  const shortTermCache = results.filter(r => r.actualMaxAge < 3600).length;
  
  console.log(`Long-term cache (≥1 day): ${longTermCache} files`);
  console.log(`Medium-term cache (1 hour - 1 day): ${mediumTermCache} files`);
  console.log(`Short-term cache (<1 hour): ${shortTermCache} files`);
  
  // Compression analysis
  const compressedFiles = results.filter(r => 
    r.headers.contentEncoding && r.headers.contentEncoding !== 'identity'
  ).length;
  console.log(`Compressed responses: ${compressedFiles} files`);
  
  const rangeSupport = results.filter(r => 
    r.headers.acceptRanges === 'bytes'
  ).length;
  console.log(`Range request support: ${rangeSupport} files`);
}

/**
 * Main test execution
 */
async function runTests() {
  console.log(`${colors.bright}FastTransfer Cache Behavior Testing${colors.reset}`);
  console.log(`Testing against: ${config.baseUrl}`);
  console.log(`${colors.cyan}Testing ${config.testFiles.length} file types...${colors.reset}`);
  
  const results = [];
  
  for (const testFile of config.testFiles) {
    const result = await testCacheBehavior(testFile);
    results.push(result);
    printResult(result);
  }
  
  generateSummary(results);
  
  // Exit with error code if any tests failed
  const failedTests = results.filter(r => !r.passed).length;
  process.exit(failedTests > 0 ? 1 : 0);
}

// Run tests if this script is executed directly
if (require.main === module) {
  runTests().catch(error => {
    console.error(`${colors.red}Test execution failed:${colors.reset}`, error);
    process.exit(1);
  });
}

module.exports = { runTests, testCacheBehavior };

import { S3Client, GetObjectCommand, PutObjectCommand } from '@aws-sdk/client-s3';
import { SQSClient, ReceiveMessageCommand, DeleteMessageCommand } from '@aws-sdk/client-sqs';
import { DynamoDBClient } from '@aws-sdk/client-dynamodb';
import { DynamoDBDocumentClient, GetCommand, UpdateCommand } from '@aws-sdk/lib-dynamodb';
import * as fs from 'fs-extra';
import * as path from 'path';
import { v4 as uuidv4 } from 'uuid';
import { CompressionRouter, CompressionMethod } from './compression-router';

// AWS Clients
const region = 'us-east-2';
const s3Client = new S3Client({ region });
const sqsClient = new SQSClient({ region });
const dynamoClient = new DynamoDBClient({ region });
const docClient = DynamoDBDocumentClient.from(dynamoClient);

// Environment variables
const UPLOAD_BUCKET = process.env.UPLOAD_BUCKET!;
const COMPRESSED_BUCKET = process.env.COMPRESSED_BUCKET!;
const DECOMPRESSED_BUCKET = process.env.DECOMPRESSED_BUCKET!;
const TRANSFER_TABLE = process.env.TRANSFER_TABLE!;
const JOB_TABLE = process.env.JOB_TABLE!;
const COMPRESSION_QUEUE_URL = process.env.COMPRESSION_QUEUE_URL!;
const DECOMPRESSION_QUEUE_URL = process.env.DECOMPRESSION_QUEUE_URL!;

// Working directories
const WORK_DIR = '/tmp/fasttransfer';
const SCRIPTS_DIR = '/opt/fasttransfer/scripts';

interface JobMessage {
  jobId?: string;
  transferId: string;
  type: 'COMPRESSION' | 'DECOMPRESSION';
  timestamp: number;
}

class FastTransferWorker {
  private isRunning = false;
  private processingJobs = new Set<string>();
  private compressionRouter!: CompressionRouter;

  async start(): Promise<void> {
    console.log('FastTransfer Worker starting...');
    this.isRunning = true;

    // Initialize compression router
    this.compressionRouter = new CompressionRouter(SCRIPTS_DIR);

    // Ensure work directory exists
    await fs.ensureDir(WORK_DIR);

    // Start polling for jobs
    this.pollForJobs();
  }

  async stop(): Promise<void> {
    console.log('FastTransfer Worker stopping...');
    this.isRunning = false;
  }

  private async pollForJobs(): Promise<void> {
    while (this.isRunning) {
      try {
        // Poll compression queue
        await this.pollQueue(COMPRESSION_QUEUE_URL, 'COMPRESSION');
        
        // Poll decompression queue
        await this.pollQueue(DECOMPRESSION_QUEUE_URL, 'DECOMPRESSION');

        // Wait before next poll
        await this.sleep(5000); // 5 seconds
      } catch (error) {
        console.error('Error polling for jobs:', error);
        await this.sleep(10000); // Wait longer on error
      }
    }
  }

  private async pollQueue(queueUrl: string, jobType: string): Promise<void> {
    const command = new ReceiveMessageCommand({
      QueueUrl: queueUrl,
      MaxNumberOfMessages: 1,
      WaitTimeSeconds: 5,
      VisibilityTimeout: 900, // 15 minutes
    });

    const result = await sqsClient.send(command);
    
    if (result.Messages && result.Messages.length > 0) {
      for (const message of result.Messages) {
        try {
          const jobMessage: JobMessage = JSON.parse(message.Body!);
          const jobKey = `${jobMessage.transferId}-${jobType}`;

          // Prevent duplicate processing
          if (this.processingJobs.has(jobKey)) {
            continue;
          }

          this.processingJobs.add(jobKey);

          if (jobType === 'COMPRESSION') {
            await this.processCompressionJob(jobMessage);
          } else {
            await this.processDecompressionJob(jobMessage);
          }

          // Delete message from queue after successful processing
          await sqsClient.send(new DeleteMessageCommand({
            QueueUrl: queueUrl,
            ReceiptHandle: message.ReceiptHandle!,
          }));

          this.processingJobs.delete(jobKey);

        } catch (error) {
          console.error(`Error processing ${jobType} job:`, error);
          this.processingJobs.delete(`${JSON.parse(message.Body!).transferId}-${jobType}`);
          // Message will be retried due to visibility timeout
        }
      }
    }
  }

  private async processCompressionJob(jobMessage: JobMessage): Promise<void> {
    console.log(`Processing compression job for transfer: ${jobMessage.transferId}`);

    const workDir = path.join(WORK_DIR, `compression-${jobMessage.transferId}`);
    await fs.ensureDir(workDir);

    try {
      // Get transfer details
      const transfer = await this.getTransfer(jobMessage.transferId);
      if (!transfer) {
        throw new Error(`Transfer not found: ${jobMessage.transferId}`);
      }

      // Download all files from upload bucket
      const downloadedFiles: string[] = [];
      for (const file of transfer.originalFiles) {
        const localPath = path.join(workDir, file.fileName);
        await this.downloadFromS3(UPLOAD_BUCKET, file.s3Key, localPath);
        downloadedFiles.push(localPath);
      }

      // Compress files using the new compression router
      const compressedPath = path.join(workDir, `${jobMessage.transferId}.zmt`);
      const compressionResult = await this.compressionRouter.compressFiles(downloadedFiles, compressedPath);

      const compressedSize = compressionResult.compressedSize;
      const compressionRatio = compressionResult.compressionRatio;

      // Upload compressed file to compressed bucket
      const compressedS3Key = `${jobMessage.transferId}.zmt`;
      await this.uploadToS3(COMPRESSED_BUCKET, compressedS3Key, compressedPath);

      // Update job status
      await this.updateJobStatus(jobMessage.jobId || uuidv4(), 'COMPLETED', {
        compressedSize,
        compressionRatio,
        compressedS3Key,
        compressionTime: compressionResult.compressionTime,
        compressionMethod: compressionResult.method,
        originalSize: compressionResult.originalSize,
      });

      console.log(`Compression completed for transfer: ${jobMessage.transferId}, ratio: ${compressionRatio}%`);

    } catch (error) {
      console.error(`Compression failed for transfer: ${jobMessage.transferId}:`, error);
      await this.updateJobStatus(jobMessage.jobId || uuidv4(), 'FAILED', undefined, error instanceof Error ? error.message : String(error));
    } finally {
      // Cleanup work directory
      await fs.remove(workDir);
    }
  }

  private async processDecompressionJob(jobMessage: JobMessage): Promise<void> {
    console.log(`Processing decompression job for transfer: ${jobMessage.transferId}`);

    const workDir = path.join(WORK_DIR, `decompression-${jobMessage.transferId}`);
    await fs.ensureDir(workDir);

    try {
      // Download compressed file from compressed bucket
      const compressedPath = path.join(workDir, `${jobMessage.transferId}.zmt`);
      const compressedS3Key = `${jobMessage.transferId}.zmt`;
      await this.downloadFromS3(COMPRESSED_BUCKET, compressedS3Key, compressedPath);

      // Get transfer details to determine compression method
      const transfer = await this.getTransfer(jobMessage.transferId);
      if (!transfer) {
        throw new Error(`Transfer not found: ${jobMessage.transferId}`);
      }

      // Determine compression method from transfer metadata or file extension
      let compressionMethod = CompressionMethod.BASIC_ZMT;
      if (transfer.compressionMethod) {
        compressionMethod = transfer.compressionMethod as CompressionMethod;
      } else if (transfer.originalFiles && transfer.originalFiles.length > 0) {
        compressionMethod = this.compressionRouter.getCompressionMethod(transfer.originalFiles[0].fileName);
      }

      // Extract files using the compression router
      const extractDir = path.join(workDir, 'extracted');
      await fs.ensureDir(extractDir);
      const decompressionResult = await this.compressionRouter.extractFiles(compressedPath, extractDir, compressionMethod);

      // Upload extracted files to decompressed bucket
      for (const extractedFilePath of decompressionResult.extractedFiles) {
        const fileName = path.basename(extractedFilePath);
        const s3Key = `${jobMessage.transferId}/${fileName}`;
        await this.uploadToS3(DECOMPRESSED_BUCKET, s3Key, extractedFilePath);
      }

      // Update job status
      await this.updateJobStatus(jobMessage.jobId!, 'COMPLETED', {
        extractedFiles: decompressionResult.extractedFiles.length,
        decompressedS3Prefix: `${jobMessage.transferId}/`,
        decompressionTime: decompressionResult.decompressionTime,
        decompressionMethod: decompressionResult.method,
      });

      console.log(`Decompression completed for transfer: ${jobMessage.transferId}, files: ${decompressionResult.extractedFiles.length}`);

    } catch (error) {
      console.error(`Decompression failed for transfer: ${jobMessage.transferId}:`, error);
      await this.updateJobStatus(jobMessage.jobId!, 'FAILED', undefined, error instanceof Error ? error.message : String(error));
    } finally {
      // Cleanup work directory
      await fs.remove(workDir);
    }
  }



  private async downloadFromS3(bucket: string, key: string, localPath: string): Promise<void> {
    const command = new GetObjectCommand({ Bucket: bucket, Key: key });
    const response = await s3Client.send(command);
    
    if (response.Body) {
      const stream = response.Body as NodeJS.ReadableStream;
      const writeStream = fs.createWriteStream(localPath);
      
      return new Promise((resolve, reject) => {
        stream.pipe(writeStream);
        writeStream.on('finish', resolve);
        writeStream.on('error', reject);
      });
    } else {
      throw new Error(`Failed to download ${key} from ${bucket}`);
    }
  }

  private async uploadToS3(bucket: string, key: string, localPath: string): Promise<void> {
    const fileStream = fs.createReadStream(localPath);
    const command = new PutObjectCommand({
      Bucket: bucket,
      Key: key,
      Body: fileStream,
    });
    
    await s3Client.send(command);
  }

  private async getTransfer(transferId: string): Promise<any> {
    const command = new GetCommand({
      TableName: TRANSFER_TABLE,
      Key: { transferId },
    });
    
    const result = await docClient.send(command);
    return result.Item;
  }

  private async updateJobStatus(jobId: string, status: string, metadata?: any, error?: string): Promise<void> {
    let updateExpression = 'SET #status = :status, updatedAt = :updatedAt';
    const expressionAttributeNames: Record<string, string> = { '#status': 'status' };
    const expressionAttributeValues: Record<string, any> = {
      ':status': status,
      ':updatedAt': Date.now(),
    };

    if (metadata) {
      updateExpression += ', metadata = :metadata';
      expressionAttributeValues[':metadata'] = metadata;
    }

    if (error) {
      updateExpression += ', errorMessage = :error';
      expressionAttributeValues[':error'] = error;
    }

    await docClient.send(new UpdateCommand({
      TableName: JOB_TABLE,
      Key: { jobId },
      UpdateExpression: updateExpression,
      ExpressionAttributeNames: expressionAttributeNames,
      ExpressionAttributeValues: expressionAttributeValues,
    }));

    // Also notify the job orchestration system
    // This could be done via API call or SNS in production
    console.log(`Job ${jobId} status updated to ${status}`);
  }

  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// Start the worker
const worker = new FastTransferWorker();

process.on('SIGINT', async () => {
  console.log('Received SIGINT, shutting down gracefully...');
  await worker.stop();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  console.log('Received SIGTERM, shutting down gracefully...');
  await worker.stop();
  process.exit(0);
});

worker.start().catch(error => {
  console.error('Worker failed to start:', error);
  process.exit(1);
});

#!/bin/bash

# Test script to verify the FastTransfer API endpoints

echo "Testing FastTransfer API endpoints..."

# Test health endpoint
echo "1. Testing health endpoint..."
curl -X GET http://localhost:3000/api/health
echo -e "\n"

# Test file upload
echo "2. Testing file upload..."
TRANSFER_ID=$(date +%s)
curl -X POST http://localhost:3000/api/upload \
  -F "file=@test-file.txt" \
  -F "transferId=$TRANSFER_ID"
echo -e "\n"

# Wait a moment
echo "3. Waiting 2 seconds..."
sleep 2

# Test status check
echo "4. Testing status check..."
curl -X GET "http://localhost:3000/api/transfer/$TRANSFER_ID/status"
echo -e "\n"

# Wait for compression to complete
echo "5. Waiting 8 seconds for compression to complete..."
sleep 8

# Test status check again
echo "6. Testing status check after compression..."
curl -X GET "http://localhost:3000/api/transfer/$TRANSFER_ID/status"
echo -e "\n"

echo "Test completed!"

import axios from 'axios';

const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:3002/api';

const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 0, // No timeout for file uploads (handles large files up to 100GB)
});

export interface UploadResponse {
  transferId: string;
  message: string;
}

export interface ChunkUploadInitResponse {
  success: boolean;
  transferId: string;
  totalChunks: number;
  chunkSize: number;
  message: string;
}

export interface ChunkUploadResponse {
  success: boolean;
  chunkIndex: number;
  uploadedChunks: number;
  totalChunks: number;
  isComplete: boolean;
  progress: string;
}

export interface ChunkFinalizeResponse {
  success: boolean;
  transferId: string;
  file: {
    originalFilename: string;
    originalSize: number;
    compressedSize: number;
    compressionRatio: number;
    compressionTime: number;
    s3Key: string;
  };
  download: {
    url: string;
    expiresAt: number;
    expiresAtFormatted: string;
  };
  processing: {
    compressionTime: number;
    method: string;
  };
  message: string;
}

export interface TransferStatus {
  transferId: string;
  status: 'uploading' | 'compressing' | 'ready' | 'error';
  downloadUrl?: string;
  compressionRatio?: number;
  compressedSize?: number;
  error?: string;
}

// Chunk size should match backend (25MB)
const CHUNK_SIZE = 25 * 1024 * 1024;

export const uploadFile = async (
  file: File,
  transferId: string,
  onProgress?: (bytesUploaded: number, totalBytes: number) => void,
  onCompressionStart?: () => void
): Promise<UploadResponse> => {
  // Use chunked upload for all files to maintain consistency
  return uploadFileInChunks(file, transferId, onProgress, onCompressionStart);
};

// Initialize chunked upload
export const initializeChunkedUpload = async (
  originalFilename: string,
  totalSize: number,
  transferId: string
): Promise<ChunkUploadInitResponse> => {
  const response = await api.post('/upload-init', {
    originalFilename,
    totalSize,
    transferId
  });

  return response.data;
};

// Upload individual chunk
export const uploadChunk = async (
  chunk: Blob,
  transferId: string,
  chunkIndex: number
): Promise<ChunkUploadResponse> => {
  const formData = new FormData();
  formData.append('chunk', chunk);
  formData.append('transferId', transferId);
  formData.append('chunkIndex', chunkIndex.toString());

  const response = await api.post('/upload-chunk', formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });

  return response.data;
};

// Finalize chunked upload
export const finalizeChunkedUpload = async (
  transferId: string
): Promise<ChunkFinalizeResponse> => {
  const response = await api.post('/upload-finalize', {
    transferId
  });

  return response.data;
};

// Main chunked upload function
export const uploadFileInChunks = async (
  file: File,
  transferId: string,
  onProgress?: (bytesUploaded: number, totalBytes: number) => void,
  onCompressionStart?: () => void
): Promise<UploadResponse> => {
  try {
    // Initialize upload
    const initResponse = await initializeChunkedUpload(file.name, file.size, transferId);

    const totalChunks = Math.ceil(file.size / CHUNK_SIZE);
    let uploadedBytes = 0;

    console.log(`🚀 Starting chunked upload: ${file.name} (${totalChunks} chunks)`);

    // Upload chunks sequentially for now (can be parallelized later)
    for (let chunkIndex = 0; chunkIndex < totalChunks; chunkIndex++) {
      const start = chunkIndex * CHUNK_SIZE;
      const end = Math.min(start + CHUNK_SIZE, file.size);
      const chunk = file.slice(start, end);

      console.log(`📤 Uploading chunk ${chunkIndex + 1}/${totalChunks} (${(chunk.size / 1024 / 1024).toFixed(2)}MB)`);

      const chunkResponse = await uploadChunk(chunk, transferId, chunkIndex);

      if (!chunkResponse.success) {
        throw new Error(`Failed to upload chunk ${chunkIndex}`);
      }

      uploadedBytes += chunk.size;

      // Report progress
      if (onProgress) {
        onProgress(uploadedBytes, file.size);
      }

      console.log(`✅ Chunk ${chunkIndex + 1}/${totalChunks} uploaded (${chunkResponse.progress}% total)`);
    }

    console.log(`🔗 Upload complete, starting compression: ${transferId}`);

    // Notify that compression is starting
    if (onCompressionStart) {
      onCompressionStart();
    }

    // Finalize upload (this will now trigger compression asynchronously)
    const finalizeResponse = await finalizeChunkedUpload(transferId);

    console.log(`✅ Upload finalized, compression in progress: ${finalizeResponse.file?.originalFilename || 'file'}`);

    return {
      transferId: finalizeResponse.transferId,
      message: finalizeResponse.message
    };

  } catch (error) {
    console.error('❌ Chunked upload failed:', error);
    throw error;
  }
};

export const getTransferStatus = async (transferId: string): Promise<TransferStatus> => {
  const response = await api.get(`/transfer/${transferId}/status`);
  return response.data;
};

export const downloadFile = async (downloadUrl: string, filename: string, downloadType: 'compressed' | 'original' = 'compressed'): Promise<void> => {
  // Handle relative URLs by removing the /api prefix since our axios instance already has it
  const cleanUrl = downloadUrl.startsWith('/api/') ? downloadUrl.substring(4) : downloadUrl;

  const response = await api.get(cleanUrl, {
    responseType: 'blob',
  });

  // Create blob link to download
  const url = window.URL.createObjectURL(new Blob([response.data]));
  const link = document.createElement('a');
  link.href = url;

  // Set filename based on download type
  let downloadFilename: string;
  if (downloadType === 'original') {
    // For original downloads, use the filename as-is (without .zmt extension)
    downloadFilename = filename.endsWith('.zmt') ? filename.slice(0, -4) : filename;
  } else {
    // For compressed downloads, add .zmt extension if not present
    downloadFilename = filename.endsWith('.zmt') ? filename : `${filename}.zmt`;
  }

  link.setAttribute('download', downloadFilename);

  // Append to html link element page
  document.body.appendChild(link);

  // Start download
  link.click();

  // Clean up and remove the link
  link.parentNode?.removeChild(link);
  window.URL.revokeObjectURL(url);
};

// Download compressed file (default behavior)
export const downloadCompressedFile = async (transferId: string, filename: string): Promise<void> => {
  const downloadUrl = `/download/${transferId}`;
  await downloadFile(downloadUrl, filename, 'compressed');
};

// Download original (uncompressed) file
export const downloadOriginalFile = async (transferId: string, filename: string): Promise<void> => {
  const downloadUrl = `/download/${transferId}/original`;
  await downloadFile(downloadUrl, filename, 'original');
};

export interface TransferHistoryItem {
  transferId: string;
  filename: string;
  size: number;
  status: 'uploading' | 'compressing' | 'ready' | 'error';
  downloadUrl?: string;
  compressionRatio?: number;
  createdAt: string;
  expiresAt?: number;
  downloadCount?: number;
  downloadLimit?: number;
}

export interface TransferHistoryResponse {
  transfers: TransferHistoryItem[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

export interface TransferHistoryParams {
  status?: string;
  search?: string;
  sortBy?: 'filename' | 'size' | 'createdAt' | 'status';
  sortOrder?: 'asc' | 'desc';
  page?: number;
  limit?: number;
  startDate?: string;
  endDate?: string;
}

export interface TransferStats {
  total: number;
  byStatus: {
    uploading: number;
    compressing: number;
    ready: number;
    error: number;
  };
  totalSize: number;
  totalCompressedSize: number;
  averageCompressionRatio: number;
  totalDownloads: number;
  recentActivity: number;
  storageUsed: number;
}

export interface BulkDeleteResponse {
  message: string;
  deleted: string[];
  failed: Array<{ transferId: string; error: string }>;
}

export interface UpdateTransferRequest {
  downloadLimit?: number;
  expirationHours?: number;
  password?: string;
}

export const getTransferHistory = async (params?: TransferHistoryParams): Promise<TransferHistoryResponse> => {
  const queryParams = new URLSearchParams();

  if (params) {
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        queryParams.append(key, value.toString());
      }
    });
  }

  const response = await api.get(`/transfers?${queryParams.toString()}`);
  return response.data;
};

export const deleteTransfer = async (transferId: string): Promise<void> => {
  await api.delete(`/transfer/${transferId}`);
};

export const bulkDeleteTransfers = async (transferIds: string[]): Promise<BulkDeleteResponse> => {
  const response = await api.post('/transfers/bulk-delete', { transferIds });
  return response.data;
};

export const updateTransfer = async (transferId: string, updates: UpdateTransferRequest): Promise<void> => {
  await api.patch(`/transfer/${transferId}`, updates);
};

export const getTransferStats = async (): Promise<TransferStats> => {
  const response = await api.get('/transfers/stats');
  return response.data;
};

export interface DecompressResponse {
  decompressId: string;
  extractedFiles: string[];
  message: string;
}

export const decompressFile = async (file: File, decompressId: string): Promise<any> => {
  const formData = new FormData();
  formData.append('file', file);
  formData.append('decompressId', decompressId);

  const response = await api.post('/decompress', formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });

  return response.data;
};

export const downloadDecompressedFile = async (decompressId: string, filename: string): Promise<void> => {
  const response = await api.get(`/decompress/${decompressId}/${filename}`, {
    responseType: 'blob',
  });

  // Create blob link to download
  const url = window.URL.createObjectURL(new Blob([response.data]));
  const link = document.createElement('a');
  link.href = url;
  link.setAttribute('download', filename);

  // Append to html link element page
  document.body.appendChild(link);

  // Start download
  link.click();

  // Clean up and remove the link
  link.parentNode?.removeChild(link);
  window.URL.revokeObjectURL(url);
};

export interface LinkRequest {
  transferId: string;
  expirationHours?: number;
  downloadLimit?: number;
  password?: string;
}

export interface LinkResponse {
  transferId: string;
  shareUrl: string;
  expiresAt: number;
  downloadLimit?: number;
}

export interface TransferInfo {
  transferId: string;
  status: string;
  originalName: string;
  size: number;
  originalSize?: number; // For AWS backend compatibility
  compressedSize?: number;
  compressionRatio?: number;
  createdAt: string;
  expiresAt?: number;
  downloadCount: number;
  downloadLimit?: number;
  hasPassword: boolean;
}

export const generateShareLink = async (request: LinkRequest): Promise<LinkResponse> => {
  const response = await api.post('/link', request);
  return response.data;
};

export const getTransferInfo = async (transferId: string): Promise<TransferInfo> => {
  const response = await api.get(`/transfer/${transferId}/info`);
  return response.data;
};

export interface EmailRequest {
  transferId: string;
  recipientEmail: string;
  senderName?: string;
}

export interface EmailResponse {
  success: boolean;
  messageId?: string;
  error?: string;
}

export const sendShareEmail = async (request: EmailRequest): Promise<EmailResponse> => {
  const response = await api.post('/email', request);
  return response.data;
};

// Analytics interfaces
export interface AnalyticsSummary {
  totalTransfers: number;
  totalUploads: number;
  totalDownloads: number;
  totalDataTransferred: number;
  totalDataCompressed: number;
  averageCompressionRatio: number;
  averageUploadSpeed: number;
  averageCompressionTime: number;
  successRate: number;
  errorRate: number;
  topFileTypes: Array<{ type: string; count: number }>;
  dailyStats: Array<{ date: string; transfers: number; dataTransferred: number }>;
}

export interface AnalyticsEvent {
  eventId: string;
  eventType: string;
  timestamp: number;
  sessionId?: string;
  transferId?: string;
  userId?: string;
  metadata: {
    fileSize?: number;
    fileName?: string;
    compressionRatio?: number;
    compressionTime?: number;
    transferSpeed?: number;
    errorMessage?: string;
    userAgent?: string;
    ipAddress?: string;
    [key: string]: any;
  };
}

export interface PerformanceMetrics {
  transferId: string;
  uploadStartTime: number;
  uploadEndTime?: number;
  compressionStartTime?: number;
  compressionEndTime?: number;
  downloadStartTime?: number;
  downloadEndTime?: number;
  originalFileSize?: number;
  compressedFileSize?: number;
  uploadSpeed?: number;
  compressionTime?: number;
  downloadSpeed?: number;
}

export interface TimeRange {
  start: number;
  end: number;
}

// Analytics API functions
export const getAnalyticsSummary = async (timeRange?: TimeRange): Promise<AnalyticsSummary> => {
  const params = timeRange ? { start: timeRange.start, end: timeRange.end } : {};
  const response = await api.get('/analytics/summary', { params });
  return response.data;
};

export const getAnalyticsEvents = async (limit: number = 100): Promise<AnalyticsEvent[]> => {
  const response = await api.get('/analytics/events', { params: { limit } });
  return response.data;
};

export const getPerformanceMetrics = async (transferId: string): Promise<PerformanceMetrics | null> => {
  try {
    const response = await api.get(`/analytics/performance/${transferId}`);
    return response.data;
  } catch (error) {
    if (axios.isAxiosError(error) && error.response?.status === 404) {
      return null;
    }
    throw error;
  }
};

// User management API functions
export interface CreateUserRequest {
  email: string;
}

export interface CreateUserResponse {
  email: string;
  status: 'pending' | 'active';
  dataUploaded: number;
  message: string;
}

export interface CompleteAccountRequest {
  email: string;
  name?: string;
  password?: string;
  token: string;
}

export interface CompleteAccountResponse {
  email: string;
  status: 'pending' | 'active';
  message: string;
}

export interface UserStatusResponse {
  email: string;
  status: 'pending' | 'active';
  dataUploaded: number;
  dataLimit: number;
  canUpload: boolean;
  upgradeRequired: boolean;
}

export interface ValidateUploadRequest {
  email: string;
  uploadSize: number;
}

export interface ValidateUploadResponse {
  canUpload: boolean;
  remainingBytes: number;
  remainingFormatted: string;
  upgradeRequired: boolean;
  message?: string;
}

export const createUser = async (request: CreateUserRequest): Promise<CreateUserResponse> => {
  const response = await api.post('/user', request);
  return response.data;
};

export const completeAccount = async (request: CompleteAccountRequest): Promise<CompleteAccountResponse> => {
  const response = await api.post('/user/complete', request);
  return response.data;
};

export const getUserStatus = async (email: string): Promise<UserStatusResponse> => {
  const response = await api.get(`/user/${encodeURIComponent(email)}/status`);
  return response.data;
};

export const validateUpload = async (request: ValidateUploadRequest): Promise<ValidateUploadResponse> => {
  const response = await api.post('/user/validate-upload', request);
  return response.data;
};

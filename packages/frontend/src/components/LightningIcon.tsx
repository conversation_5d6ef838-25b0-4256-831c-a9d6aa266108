import { Zap } from 'lucide-react';

interface LightningIconProps {
  className?: string;
  size?: number;
  usePng?: boolean;
}

export function LightningIcon({ className = '', size, usePng = false }: LightningIconProps) {
  // If PNG is preferred and available, use it
  if (usePng) {
    return (
      <img 
        src="/lasso.png" 
        alt="Lightning" 
        className={className}
        style={{ 
          width: size ? `${size}px` : undefined, 
          height: size ? `${size}px` : undefined 
        }}
      />
    );
  }
  
  // Fallback to Lucide Zap icon
  return <Zap className={className} />;
}

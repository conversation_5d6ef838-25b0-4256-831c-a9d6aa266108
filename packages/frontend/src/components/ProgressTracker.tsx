import { Clock, Archive, CheckCircle, AlertCircle, TrendingUp } from 'lucide-react';
import type { ProgressState } from '../hooks/useProgressTracking';
import { LightningIcon } from './LightningIcon';

interface ProgressTrackerProps {
  progress: ProgressState;
  fileName: string;
  fileSize: number;
  formatSpeed: (speed: number) => string;
  formatTime: (time: number) => string;
  formatFileSize: (size: number) => string;
}

export function ProgressTracker({
  progress,
  fileName,
  fileSize,
  formatSpeed,
  formatTime,
  formatFileSize
}: ProgressTrackerProps) {

  // Detect file types that have longer/unpredictable compression times
  const getFileType = (fileName: string) => {
    const ext = fileName.toLowerCase().split('.').pop() || '';

    const audioExtensions = ['mp3', 'wav', 'flac', 'aac', 'ogg', 'wma', 'm4a'];
    const videoExtensions = ['mp4', 'avi', 'mov', 'mkv', 'wmv', 'flv', 'webm', 'y4m'];
    const largeFileThreshold = 100 * 1024 * 1024; // 100MB

    if (audioExtensions.includes(ext)) return 'audio';
    if (videoExtensions.includes(ext)) return 'video';
    if (fileSize > largeFileThreshold) return 'large';
    return 'standard';
  };

  const fileType = getFileType(fileName);
  const shouldShowProgressBar = fileType === 'standard' && progress.phase === 'compressing';
  const getPhaseIcon = () => {
    switch (progress.phase) {
      case 'uploading':
        return <LightningIcon className="w-5 h-5 text-primary animate-pulse" usePng={true} />;
      case 'compressing':
        return <Archive className="w-5 h-5 text-orange-500 animate-spin" />;
      case 'ready':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'error':
        return <AlertCircle className="w-5 h-5 text-red-500" />;
      default:
        return <Clock className="w-5 h-5 text-gray-400" />;
    }
  };

  const getPhaseText = () => {
    switch (progress.phase) {
      case 'uploading':
        return 'Uploading file...';
      case 'compressing':
        if (fileType === 'audio') {
          return 'Running audio compression...';
        } else if (fileType === 'video') {
          return 'Running video compression...';
        } else if (fileType === 'large') {
          return 'Running compression on large file...';
        } else {
          return 'Compressing with ZMT technology...';
        }
      case 'ready':
        return 'Ready for download!';
      case 'error':
        return progress.error || 'An error occurred';
      default:
        return 'Waiting...';
    }
  };

  const getPhaseColor = () => {
    switch (progress.phase) {
      case 'uploading':
        return 'text-primary';
      case 'compressing':
        return 'text-orange-600';
      case 'ready':
        return 'text-green-600';
      case 'error':
        return 'text-red-600';
      default:
        return 'text-gray-600';
    }
  };

  const getProgressBarColor = () => {
    switch (progress.phase) {
      case 'uploading':
        return 'progress-info';
      case 'compressing':
        return 'progress-warning';
      case 'ready':
        return 'progress-success';
      case 'error':
        return 'progress-error';
      default:
        return 'progress-primary';
    }
  };

  const getCurrentProgress = () => {
    if (progress.phase === 'uploading') {
      return progress.uploadProgress;
    } else if (progress.phase === 'compressing') {
      return progress.compressionProgress;
    } else if (progress.phase === 'ready') {
      return 100;
    }
    return 0;
  };

  return (
    <div className="bg-base-100 rounded-2xl p-6 shadow-sm border" style={{borderColor: '#313131'}}>
      {/* Header */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-3">
          {getPhaseIcon()}
          <div>
            <h3 className="font-semibold text-base-content truncate max-w-xs" title={fileName}>
              {fileName}
            </h3>
            <p className={`text-sm ${getPhaseColor()}`}>
              {getPhaseText()}
            </p>
          </div>
        </div>
        <div className="text-right">
          <p className="text-sm font-medium text-base-content">
            {formatFileSize(fileSize)}
          </p>
          {progress.totalTime > 0 && (
            <p className="text-xs text-base-content/60">
              {formatTime(progress.totalTime)}
            </p>
          )}
        </div>
      </div>

      {/* Progress Bar or Running Indicator */}
      {progress.phase !== 'idle' && progress.phase !== 'error' && (
        <div className="mb-4">
          {progress.phase === 'uploading' || shouldShowProgressBar ? (
            // Show progress bar for uploads and standard file compression
            <>
              <div className="flex justify-between items-center mb-2">
                <span className="text-sm font-medium text-base-content">
                  {progress.phase === 'uploading' ? 'Upload' : 'Compression'} Progress
                </span>
                <span className="text-sm text-base-content/70">
                  {Math.round(getCurrentProgress())}%
                </span>
              </div>
              <progress
                className={`progress w-full ${getProgressBarColor()}`}
                value={getCurrentProgress()}
                max="100"
              />
            </>
          ) : (
            // Show running indicator for audio, video, and large files
            <div className="flex items-center justify-center space-x-3 py-4 bg-orange-50 dark:bg-orange-900/20 rounded-lg border border-orange-200 dark:border-orange-800">
              <Archive className="w-5 h-5 text-orange-500 animate-spin" />
              <div className="text-center">
                <div className="text-sm font-medium text-orange-700 dark:text-orange-300">
                  Running compression
                </div>
                <div className="text-xs text-orange-600 dark:text-orange-400 mt-1">
                  {fileType === 'audio' && 'Audio files may take longer to compress'}
                  {fileType === 'video' && 'Video files may take longer to compress'}
                  {fileType === 'large' && 'Large files may take longer to compress'}
                </div>
              </div>
            </div>
          )}
        </div>
      )}

      {/* Detailed Stats */}
      {(progress.phase === 'uploading' || progress.phase === 'compressing') && (
        <div className="grid grid-cols-2 gap-4 text-sm">
          {progress.speed > 0 && (
            <div className="flex items-center space-x-2">
              <TrendingUp className="w-4 h-4 text-primary" />
              <div>
                <p className="text-base-content/60">Speed</p>
                <p className="font-medium text-base-content">{formatSpeed(progress.speed)}</p>
              </div>
            </div>
          )}
          
          {progress.timeRemaining > 0 && progress.phase === 'uploading' && (
            <div className="flex items-center space-x-2">
              <Clock className="w-4 h-4 text-orange-500" />
              <div>
                <p className="text-base-content/60">Time Remaining</p>
                <p className="font-medium text-base-content">{formatTime(progress.timeRemaining)}</p>
              </div>
            </div>
          )}
        </div>
      )}

      {/* Dual Progress for Compression Phase */}
      {progress.phase === 'compressing' && shouldShowProgressBar && (
        <div className="mt-4 space-y-3">
          {/* Upload Progress (completed) */}
          <div>
            <div className="flex justify-between items-center mb-1">
              <span className="text-xs text-base-content/60">Upload</span>
              <span className="text-xs text-green-600">✓ Complete</span>
            </div>
            <progress className="progress progress-success w-full" value="100" max="100" />
          </div>

          {/* Compression Progress (active) */}
          <div>
            <div className="flex justify-between items-center mb-1">
              <span className="text-xs text-base-content/60">Compression</span>
              <span className="text-xs text-orange-600">
                {Math.round(progress.compressionProgress)}%
              </span>
            </div>
            <progress
              className="progress progress-warning w-full"
              value={progress.compressionProgress}
              max="100"
            />
          </div>
        </div>
      )}

      {/* Success State */}
      {progress.phase === 'ready' && (
        <div className="mt-4 p-3 bg-success/10 rounded-lg border border-success/20">
          <div className="flex items-center space-x-2">
            <CheckCircle className="w-5 h-5 text-success" />
            <div>
              <p className="text-sm font-medium text-success">Processing Complete!</p>
              <p className="text-xs text-success/80">
                File compressed and ready for download
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Error State */}
      {progress.phase === 'error' && (
        <div className="mt-4 p-3 bg-error/10 rounded-lg border border-error/20">
          <div className="flex items-center space-x-2">
            <AlertCircle className="w-5 h-5 text-error" />
            <div>
              <p className="text-sm font-medium text-error">Processing Failed</p>
              <p className="text-xs text-error/80">
                {progress.error || 'An unexpected error occurred'}
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

// Mini progress component for compact display
export function MiniProgressTracker({
  progress,
  fileName,
  fileSize = 0
}: {
  progress: ProgressState;
  fileName: string;
  fileSize?: number;
}) {

  // Detect file types that have longer/unpredictable compression times
  const getFileType = (fileName: string, fileSize: number) => {
    const ext = fileName.toLowerCase().split('.').pop() || '';

    const audioExtensions = ['mp3', 'wav', 'flac', 'aac', 'ogg', 'wma', 'm4a'];
    const videoExtensions = ['mp4', 'avi', 'mov', 'mkv', 'wmv', 'flv', 'webm', 'y4m'];
    const largeFileThreshold = 100 * 1024 * 1024; // 100MB

    if (audioExtensions.includes(ext)) return 'audio';
    if (videoExtensions.includes(ext)) return 'video';
    if (fileSize > largeFileThreshold) return 'large';
    return 'standard';
  };

  const fileType = getFileType(fileName, fileSize);
  const shouldShowProgressBar = fileType === 'standard' && progress.phase === 'compressing';
  const getPhaseIcon = () => {
    switch (progress.phase) {
      case 'uploading':
        return <LightningIcon className="w-4 h-4 text-primary animate-pulse" usePng={true} />;
      case 'compressing':
        return <Archive className="w-4 h-4 text-orange-500 animate-spin" />;
      case 'ready':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'error':
        return <AlertCircle className="w-4 h-4 text-red-500" />;
      default:
        return <Clock className="w-4 h-4 text-gray-400" />;
    }
  };

  const getCurrentProgress = () => {
    if (progress.phase === 'uploading') {
      return progress.uploadProgress;
    } else if (progress.phase === 'compressing') {
      return progress.compressionProgress;
    } else if (progress.phase === 'ready') {
      return 100;
    }
    return 0;
  };

  const getProgressBarColor = () => {
    switch (progress.phase) {
      case 'uploading':
        return 'progress-info';
      case 'compressing':
        return 'progress-warning';
      case 'ready':
        return 'progress-success';
      case 'error':
        return 'progress-error';
      default:
        return 'progress-primary';
    }
  };

  return (
    <div className="flex items-center space-x-3 p-3 bg-base-100 rounded-lg border" style={{borderColor: '#313131'}}>
      {getPhaseIcon()}
      <div className="flex-1 min-w-0">
        <p className="text-sm font-medium text-base-content truncate" title={fileName}>
          {fileName}
        </p>
        {progress.phase !== 'idle' && progress.phase !== 'error' && (
          <div className="mt-1">
            {progress.phase === 'uploading' || shouldShowProgressBar ? (
              // Show progress bar for uploads and standard file compression
              <div className="flex items-center space-x-2">
                <progress
                  className={`progress progress-xs w-full ${getProgressBarColor()}`}
                  value={getCurrentProgress()}
                  max="100"
                />
                <span className="text-xs text-base-content/60 whitespace-nowrap">
                  {Math.round(getCurrentProgress())}%
                </span>
              </div>
            ) : (
              // Show running indicator for audio, video, and large files
              <div className="flex items-center space-x-2">
                <Archive className="w-3 h-3 text-orange-500 animate-spin" />
                <span className="text-xs text-orange-600 dark:text-orange-400">
                  Running compression...
                </span>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
}

module.exports = {
  apps: [
    {
      name: "fasttransfer-backend",
      script: "./services/backend/dist/server.js",
      env: {
        NODE_ENV: "production",
        PORT: 3001,
        
        // AWS Credentials
        AWS_REGION: "us-east-2", // Change to us-east-2
        AWS_ACCESS_KEY_ID: "********************",
        AWS_SECRET_ACCESS_KEY: "D0mwXr+Wr7lveBCcAD8K5OhGsWWFIElWeUEr4bTR",
        
        // S3 Buckets
        UPLOAD_BUCKET: "transfer-yeehawboost-upload",
        COMPRESSED_BUCKET: "transfer-yeehawboost-compressed",
        DECOMPRESSED_BUCKET: "transfer-yeehawboost-decompressed",
        
        // Frontend URL
        FRONTEND_URL: "https://transfer.yeehawboost.com",
        
        // File size limits
        MAX_FILE_SIZE: "107374182400", // 100GB
        
        // SendGrid (email service)
        SENDGRID_API_KEY: "*********************************************************************",
        FROM_EMAIL: "<EMAIL>",
        
        // DynamoDB Tables
        TRANSFER_TABLE: "FastTransfer-Transfers",
        USER_TABLE: "FastTransfer-Users",
        JOB_TABLE: "FastTransfer-Jobs",
        
        // SQS Configuration
        COMPRESSION_QUEUE_URL: "https://sqs.us-east-2.amazonaws.com/117255351486/FastTransfer-CompressionJobs",
        DECOMPRESSION_QUEUE_URL: "https://sqs.us-east-2.amazonaws.com/117255351486/FastTransfer-DecompressionJobs",
        
        // CloudFront Configuration
        CLOUDFRONT_DOMAIN: "d1j3tjpa9wjs97.cloudfront.net",
        CLOUDFRONT_DISTRIBUTION_ID: "E9MZE1D4PTZAR",
        
        // Base URL
        BASE_URL: "https://transfer.yeehawboost.com",
        
        // Logging
        LOG_LEVEL: "debug"
      },
      instances: 1,
      autorestart: true,
      watch: false,
      max_memory_restart: "1G",
      error_file: "./logs/backend-error.log",
      out_file: "./logs/backend-out.log",
      merge_logs: true,
      log_date_format: "YYYY-MM-DD HH:mm:ss Z"
    }
  ]
};

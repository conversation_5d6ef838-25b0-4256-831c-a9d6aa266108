#!/bin/bash

# FastTransfer Infrastructure Deployment Script
set -e

# Configuration
STACK_NAME="fasttransfer"
ENVIRONMENT=${1:-dev}
AWS_REGION=${2:-us-east-2}
KEY_PAIR_NAME=${3:-""}

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Functions
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Validate inputs
if [ -z "$KEY_PAIR_NAME" ]; then
    log_error "Key pair name is required"
    echo "Usage: $0 <environment> <aws-region> <key-pair-name>"
    echo "Example: $0 dev us-east-2 my-key-pair"
    exit 1
fi

# Check if AWS CLI is installed and configured
if ! command -v aws &> /dev/null; then
    log_error "AWS CLI is not installed"
    exit 1
fi

# Check AWS credentials
if ! aws sts get-caller-identity &> /dev/null; then
    log_error "AWS credentials not configured"
    exit 1
fi

# Validate key pair exists
if ! aws ec2 describe-key-pairs --key-names "$KEY_PAIR_NAME" --region "$AWS_REGION" &> /dev/null; then
    log_error "Key pair '$KEY_PAIR_NAME' does not exist in region '$AWS_REGION'"
    exit 1
fi

log_info "Starting deployment of FastTransfer infrastructure..."
log_info "Environment: $ENVIRONMENT"
log_info "Region: $AWS_REGION"
log_info "Key Pair: $KEY_PAIR_NAME"

# Deploy CloudFormation stack
FULL_STACK_NAME="${STACK_NAME}-${ENVIRONMENT}"

log_info "Deploying CloudFormation stack: $FULL_STACK_NAME"

aws cloudformation deploy \
    --template-file cloudformation/fasttransfer-stack.yaml \
    --stack-name "$FULL_STACK_NAME" \
    --parameter-overrides \
        Environment="$ENVIRONMENT" \
        KeyPairName="$KEY_PAIR_NAME" \
    --capabilities CAPABILITY_NAMED_IAM \
    --region "$AWS_REGION" \
    --no-fail-on-empty-changeset

if [ $? -eq 0 ]; then
    log_info "CloudFormation stack deployed successfully"
else
    log_error "CloudFormation stack deployment failed"
    exit 1
fi

# Get stack outputs
log_info "Retrieving stack outputs..."

S3_BUCKET=$(aws cloudformation describe-stacks \
    --stack-name "$FULL_STACK_NAME" \
    --region "$AWS_REGION" \
    --query 'Stacks[0].Outputs[?OutputKey==`S3BucketName`].OutputValue' \
    --output text)

CLOUDFRONT_DOMAIN=$(aws cloudformation describe-stacks \
    --stack-name "$FULL_STACK_NAME" \
    --region "$AWS_REGION" \
    --query 'Stacks[0].Outputs[?OutputKey==`CloudFrontDomainName`].OutputValue' \
    --output text)

BACKEND_IP=$(aws cloudformation describe-stacks \
    --stack-name "$FULL_STACK_NAME" \
    --region "$AWS_REGION" \
    --query 'Stacks[0].Outputs[?OutputKey==`BackendPublicIP`].OutputValue' \
    --output text)

WORKER_IP=$(aws cloudformation describe-stacks \
    --stack-name "$FULL_STACK_NAME" \
    --region "$AWS_REGION" \
    --query 'Stacks[0].Outputs[?OutputKey==`WorkerPublicIP`].OutputValue' \
    --output text)

# Display deployment information
log_info "Deployment completed successfully!"
echo ""
echo "=== FastTransfer Infrastructure Details ==="
echo "S3 Bucket: $S3_BUCKET"
echo "CloudFront Domain: $CLOUDFRONT_DOMAIN"
echo "Backend Server IP: $BACKEND_IP"
echo "Worker Server IP: $WORKER_IP"
echo ""
echo "=== Next Steps ==="
echo "1. SSH to backend server: ssh -i ~/.ssh/$KEY_PAIR_NAME.pem ec2-user@$BACKEND_IP"
echo "2. SSH to worker server: ssh -i ~/.ssh/$KEY_PAIR_NAME.pem ec2-user@$WORKER_IP"
echo "3. Deploy application code to the servers"
echo "4. Configure environment variables"
echo "5. Start the services using PM2"
echo ""
echo "=== Environment Variables for Application ==="
echo "AWS_REGION=$AWS_REGION"
echo "S3_BUCKET=$S3_BUCKET"
echo "CLOUDFRONT_DOMAIN=$CLOUDFRONT_DOMAIN"
echo ""

log_info "Infrastructure deployment completed!"

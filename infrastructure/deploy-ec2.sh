#!/bin/bash

# FastTransfer EC2 Infrastructure Deployment Script
# This script deploys the EC2 upload infrastructure using CloudFormation

set -e

# Default values
ENVIRONMENT="dev"
REGION="us-east-2"
INSTANCE_TYPE="c5.2xlarge"
STACK_NAME=""
KEY_PAIR=""
VPC_ID=""
SUBNET_IDS=""
SSL_CERT_ARN=""
DOMAIN_NAME="upload.fasttransfer.com"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to show usage
show_usage() {
    cat << EOF
Usage: $0 [OPTIONS]

Deploy FastTransfer EC2 Upload Infrastructure

OPTIONS:
    -e, --environment ENV       Environment (dev, staging, prod) [default: dev]
    -r, --region REGION         AWS region [default: us-east-2]
    -t, --instance-type TYPE    EC2 instance type [default: c5.2xlarge]
    -s, --stack-name NAME       CloudFormation stack name [default: fasttransfer-ec2-ENV]
    -k, --key-pair NAME         EC2 Key Pair name (required)
    -v, --vpc-id ID             VPC ID (required)
    -n, --subnet-ids IDS        Comma-separated subnet IDs (required)
    -c, --ssl-cert-arn ARN      SSL certificate ARN (required)
    -d, --domain-name DOMAIN    Domain name [default: upload.fasttransfer.com]
    -h, --help                  Show this help message

EXAMPLES:
    # Deploy development environment
    $0 -e dev -k my-key-pair -v vpc-12345 -n subnet-123,subnet-456 -c arn:aws:acm:us-east-2:123456789012:certificate/12345678-1234-1234-1234-123456789012

    # Deploy production environment with custom instance type
    $0 -e prod -t c5.4xlarge -k prod-key-pair -v vpc-prod123 -n subnet-prod1,subnet-prod2 -c arn:aws:acm:us-east-2:123456789012:certificate/prod-cert

EOF
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -e|--environment)
            ENVIRONMENT="$2"
            shift 2
            ;;
        -r|--region)
            REGION="$2"
            shift 2
            ;;
        -t|--instance-type)
            INSTANCE_TYPE="$2"
            shift 2
            ;;
        -s|--stack-name)
            STACK_NAME="$2"
            shift 2
            ;;
        -k|--key-pair)
            KEY_PAIR="$2"
            shift 2
            ;;
        -v|--vpc-id)
            VPC_ID="$2"
            shift 2
            ;;
        -n|--subnet-ids)
            SUBNET_IDS="$2"
            shift 2
            ;;
        -c|--ssl-cert-arn)
            SSL_CERT_ARN="$2"
            shift 2
            ;;
        -d|--domain-name)
            DOMAIN_NAME="$2"
            shift 2
            ;;
        -h|--help)
            show_usage
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Set default stack name if not provided
if [ -z "$STACK_NAME" ]; then
    STACK_NAME="fasttransfer-ec2-$ENVIRONMENT"
fi

# Validate required parameters
if [ -z "$KEY_PAIR" ]; then
    print_error "Key pair name is required. Use -k or --key-pair option."
    exit 1
fi

if [ -z "$VPC_ID" ]; then
    print_error "VPC ID is required. Use -v or --vpc-id option."
    exit 1
fi

if [ -z "$SUBNET_IDS" ]; then
    print_error "Subnet IDs are required. Use -n or --subnet-ids option."
    exit 1
fi

if [ -z "$SSL_CERT_ARN" ]; then
    print_error "SSL certificate ARN is required. Use -c or --ssl-cert-arn option."
    exit 1
fi

# Validate environment
if [[ ! "$ENVIRONMENT" =~ ^(dev|staging|prod)$ ]]; then
    print_error "Environment must be one of: dev, staging, prod"
    exit 1
fi

# Get script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
TEMPLATE_FILE="$SCRIPT_DIR/cloudformation/ec2-upload-infrastructure.yaml"

# Check if template file exists
if [ ! -f "$TEMPLATE_FILE" ]; then
    print_error "CloudFormation template not found: $TEMPLATE_FILE"
    exit 1
fi

# Check if AWS CLI is installed
if ! command -v aws &> /dev/null; then
    print_error "AWS CLI is not installed. Please install it first."
    exit 1
fi

# Check AWS credentials
if ! aws sts get-caller-identity &> /dev/null; then
    print_error "AWS credentials not configured. Please run 'aws configure' first."
    exit 1
fi

print_status "Starting deployment of FastTransfer EC2 infrastructure..."
print_status "Environment: $ENVIRONMENT"
print_status "Region: $REGION"
print_status "Stack Name: $STACK_NAME"
print_status "Instance Type: $INSTANCE_TYPE"
print_status "Key Pair: $KEY_PAIR"
print_status "VPC ID: $VPC_ID"
print_status "Subnet IDs: $SUBNET_IDS"
print_status "Domain: $DOMAIN_NAME"

# Check if stack already exists
if aws cloudformation describe-stacks --stack-name "$STACK_NAME" --region "$REGION" &> /dev/null; then
    print_warning "Stack $STACK_NAME already exists. This will update the existing stack."
    read -p "Do you want to continue? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        print_status "Deployment cancelled."
        exit 0
    fi
    OPERATION="update-stack"
else
    print_status "Creating new stack: $STACK_NAME"
    OPERATION="create-stack"
fi

# Deploy the stack
print_status "Deploying CloudFormation stack..."

aws cloudformation $OPERATION \
    --stack-name "$STACK_NAME" \
    --template-body "file://$TEMPLATE_FILE" \
    --parameters \
        ParameterKey=Environment,ParameterValue="$ENVIRONMENT" \
        ParameterKey=InstanceType,ParameterValue="$INSTANCE_TYPE" \
        ParameterKey=KeyPairName,ParameterValue="$KEY_PAIR" \
        ParameterKey=VpcId,ParameterValue="$VPC_ID" \
        ParameterKey=SubnetIds,ParameterValue="$SUBNET_IDS" \
        ParameterKey=SSLCertificateArn,ParameterValue="$SSL_CERT_ARN" \
        ParameterKey=DomainName,ParameterValue="$DOMAIN_NAME" \
    --capabilities CAPABILITY_NAMED_IAM \
    --region "$REGION" \
    --tags \
        Key=Environment,Value="$ENVIRONMENT" \
        Key=Project,Value=FastTransfer \
        Key=Component,Value=EC2Upload

if [ $? -eq 0 ]; then
    print_success "CloudFormation stack deployment initiated successfully!"
    print_status "Waiting for stack deployment to complete..."
    
    # Wait for stack to complete
    aws cloudformation wait stack-${OPERATION%-stack}-complete \
        --stack-name "$STACK_NAME" \
        --region "$REGION"
    
    if [ $? -eq 0 ]; then
        print_success "Stack deployment completed successfully!"
        
        # Get stack outputs
        print_status "Retrieving stack outputs..."
        OUTPUTS=$(aws cloudformation describe-stacks \
            --stack-name "$STACK_NAME" \
            --region "$REGION" \
            --query 'Stacks[0].Outputs' \
            --output table)
        
        echo
        print_success "Deployment Summary:"
        echo "$OUTPUTS"
        
        # Get load balancer DNS name
        LB_DNS=$(aws cloudformation describe-stacks \
            --stack-name "$STACK_NAME" \
            --region "$REGION" \
            --query 'Stacks[0].Outputs[?OutputKey==`LoadBalancerDNS`].OutputValue' \
            --output text)
        
        echo
        print_success "Your FastTransfer upload service is now available at:"
        print_success "https://$LB_DNS"
        
        if [ "$DOMAIN_NAME" != "upload.fasttransfer.com" ]; then
            print_status "Don't forget to create a CNAME record pointing $DOMAIN_NAME to $LB_DNS"
        fi
        
        print_status "Next steps:"
        print_status "1. Deploy your application code to the EC2 instances"
        print_status "2. Configure your DNS to point to the load balancer"
        print_status "3. Test the upload functionality"
        
    else
        print_error "Stack deployment failed. Check the CloudFormation console for details."
        exit 1
    fi
else
    print_error "Failed to initiate stack deployment."
    exit 1
fi

print_success "Deployment completed successfully!"

# FastTransfer

A high-performance file compression and transfer platform built with AWS-native architecture and ZMT compression technology.

## 🚀 Features

- **Ultra-fast compression** using ZMT technology
- **AWS-native architecture** with S3, CloudFront, SQS, and DynamoDB
- **Real-time progress tracking** with WebSocket connections
- **Scalable microservices** architecture
- **Modern React frontend** with drag-and-drop file uploads
- **Secure file transfers** with presigned URLs and encryption
- **Automatic cleanup** with configurable TTL policies

## 🏗️ Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   React App     │    │   Backend API   │    │  Worker Service │
│   (Frontend)    │◄──►│   (Express.js)  │◄──►│   (Node.js)     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │                       ▼                       ▼
         │              ┌─────────────────┐    ┌─────────────────┐
         │              │   DynamoDB      │    │      SQS        │
         │              │  (Metadata)     │    │   (Job Queue)   │
         │              └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   CloudFront    │    │       S3        │    │   ZMT Engine    │
│     (CDN)       │    │  (File Storage) │    │ (Compression)   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 📁 Project Structure

```
fast-transfer/
├── packages/
│   ├── frontend/          # React application with Vite
│   └── shared/            # Shared utilities and types
├── services/
│   ├── backend/           # Express.js REST API
│   └── worker/            # File processing worker
├── infrastructure/
│   ├── cloudformation/    # AWS CloudFormation templates
│   └── deploy.sh          # Deployment script
└── docs/                  # Documentation
```

## 🛠️ Technology Stack

### Frontend
- **React 18** with TypeScript
- **Vite** for fast development and building
- **Tailwind CSS** for styling
- **React Dropzone** for file uploads
- **Lucide React** for icons
- **Axios** for API communication

### Backend
- **Node.js** with Express.js
- **TypeScript** for type safety
- **AWS SDK v3** for cloud services
- **Multer** for file upload handling
- **WebSocket** for real-time updates
- **JWT** for authentication

### Infrastructure
- **AWS S3** for file storage
- **AWS CloudFront** for CDN
- **AWS SQS** for job queuing
- **AWS DynamoDB** for metadata storage
- **AWS EC2** for compute instances
- **CloudFormation** for infrastructure as code

### Compression
- **ZMT Technology** for ultra-fast compression
- **Custom compression workers** for parallel processing

## Quick Start

1. **Prerequisites**
   - Node.js 18+
   - AWS CLI configured
   - Docker (for local development)

2. **Installation**
   ```bash
   npm install
   ```

3. **Development**
   ```bash
   npm run dev
   ```

4. **Deployment**
   ```bash
   npm run deploy:infra
   npm run deploy:backend
   npm run deploy:frontend
   ```

## Development Workflow

1. **Infrastructure**: Deploy AWS resources first
2. **Backend**: Deploy Lambda functions and APIs
3. **Frontend**: Build and deploy React application
4. **Workers**: Configure EC2 instances with ZMT

## Key Components

- **Upload Flow**: Multipart upload → S3 → SQS → EC2 compression
- **Download Flow**: Link validation → Decompression → CloudFront delivery
- **Compression**: ZMT binary on Ubuntu EC2 instances
- **Monitoring**: CloudWatch metrics and alarms

## Performance Targets

- Upload speed: Optimized with S3 Transfer Acceleration
- Compression ratio: Target 80% size reduction
- Global delivery: <2s first byte via CloudFront
- Scalability: Auto-scaling based on queue depth

## Security

- All transfers over HTTPS
- Pre-signed URLs for access control
- IAM roles with least privilege
- Encryption at rest (AES-256)

## 📋 Environment Variables

### Backend Service
```env
PORT=3000
AWS_REGION=us-east-2
S3_BUCKET=fasttransfer-files-dev-123456789
DYNAMODB_TABLE=fasttransfer-transfers-dev
COMPRESSION_QUEUE_URL=https://sqs.us-east-2.amazonaws.com/123456789/fasttransfer-compression-dev
JWT_SECRET=your-jwt-secret
```

### Worker Service
```env
AWS_REGION=us-east-2
S3_BUCKET=fasttransfer-files-dev-123456789
COMPRESSION_QUEUE_URL=https://sqs.us-east-2.amazonaws.com/123456789/fasttransfer-compression-dev
DECOMPRESSION_QUEUE_URL=https://sqs.us-east-2.amazonaws.com/123456789/fasttransfer-decompression-dev
ZMT_PATH=/usr/local/bin/zmt
```

### Frontend
```env
VITE_API_URL=http://localhost:3000/api
VITE_AWS_REGION=us-east-2
VITE_MAX_FILE_SIZE=107374182400
```

## 🔧 Development Scripts

```bash
# Root level
npm run build          # Build all packages
npm run test           # Run all tests
npm run lint           # Lint all packages

# Frontend
npm run dev            # Start development server
npm run build          # Build for production
npm run preview        # Preview production build

# Backend
npm run dev            # Start with nodemon
npm run build          # Compile TypeScript
npm run start          # Start production server

# Worker
npm run dev            # Start with nodemon
npm run build          # Compile TypeScript
npm run start          # Start production worker
```

## 🚀 Production Deployment

1. **Deploy Infrastructure**
```bash
cd infrastructure
./deploy.sh prod us-east-2 your-production-key-pair
```

2. **Build Applications**
```bash
npm run build
```

3. **Deploy to EC2 Instances**
```bash
# SSH to backend server and deploy
ssh -i ~/.ssh/your-key.pem ec2-user@<backend-ip>
cd backend && npm install --production
pm2 start ecosystem.config.js
```

## 📊 Monitoring & Security

- **CloudWatch Logs** for application logs
- **CloudWatch Metrics** for performance monitoring
- **SQS Dead Letter Queues** for failed job tracking
- **IAM Roles** with least privilege access
- **VPC** with private subnets for workers
- **JWT Authentication** for API access
- **HTTPS/TLS** encryption in transit

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

For support and questions:
- Create an issue in the GitHub repository
- Check the [documentation](docs/) folder
- Review the [troubleshooting guide](docs/troubleshooting.md)
